#!/usr/bin/env python3
"""
测试文件名解析功能
"""

import re
import os

def extract_patient_id(file_path):
    """
    从文件路径中提取病人ID
    支持两种格式:
    - liver_123_045.h5 -> 123
    - MSWAL_0001_0000_001.h5 -> 1
    """
    filename = os.path.basename(file_path)
    
    # 尝试匹配 liver_XXX_YYY.h5 格式
    match = re.match(r'liver_(\d+)_\d+\.h5', filename)
    if match:
        return int(match.group(1))
    
    # 尝试匹配 MSWAL_XXXX_YYYY_ZZZ.h5 格式
    match = re.match(r'MSWAL_(\d+)_\d+_\d+\.h5', filename)
    if match:
        return int(match.group(1))
    
    return None

def extract_slice_id(file_path):
    """
    从文件路径中提取切片ID
    支持两种格式:
    - liver_123_045.h5 -> 45
    - MSWAL_0001_0000_001.h5 -> 1
    """
    filename = os.path.basename(file_path)
    
    # 尝试匹配 liver_XXX_YYY.h5 格式
    match = re.match(r'liver_\d+_(\d+)\.h5', filename)
    if match:
        return int(match.group(1))
    
    # 尝试匹配 MSWAL_XXXX_YYYY_ZZZ.h5 格式
    match = re.match(r'MSWAL_\d+_\d+_(\d+)\.h5', filename)
    if match:
        return int(match.group(1))
    
    return None

def test_parsing():
    """测试文件名解析"""
    test_files = [
        # 旧格式
        "liver_123_045.h5",
        "liver_1_001.h5",
        "/path/to/liver_456_789.h5",
        
        # 新格式
        "MSWAL_0001_0000_001.h5",
        "MSWAL_0002_0000_005.h5",
        "/home/<USER>/data/liver_tumor/h5/MSWAL_0001_0000_001.h5",
        
        # 无效格式
        "invalid_file.h5",
        "MSWAL_001_002.h5",
    ]
    
    print("🔍 测试文件名解析功能")
    print("=" * 80)
    print(f"{'文件名':<50} {'病人ID':<10} {'切片ID':<10}")
    print("-" * 80)
    
    for file_path in test_files:
        patient_id = extract_patient_id(file_path)
        slice_id = extract_slice_id(file_path)
        filename = os.path.basename(file_path)
        
        patient_str = str(patient_id) if patient_id is not None else "None"
        slice_str = str(slice_id) if slice_id is not None else "None"
        
        print(f"{filename:<50} {patient_str:<10} {slice_str:<10}")

if __name__ == "__main__":
    test_parsing()
