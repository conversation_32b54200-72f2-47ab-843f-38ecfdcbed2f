#!/usr/bin/env python3
"""
将2d_all_test.list中同一个病人的所有切片合并成一个H5文件 - 多进程版本
"""

import h5py
import numpy as np
import os
import re
from collections import defaultdict
from tqdm import tqdm
from pathlib import Path
from multiprocessing import Pool
import time
import argparse

def extract_patient_id(file_path):
    """
    从文件路径中提取病人ID
    例如: liver_123_045.h5 -> 123
    """
    filename = os.path.basename(file_path)
    match = re.match(r'liver_(\d+)_\d+\.h5', filename)
    if match:
        return int(match.group(1))
    return None

def extract_slice_id(file_path):
    """
    从文件路径中提取切片ID
    例如: liver_123_045.h5 -> 45
    """
    filename = os.path.basename(file_path)
    match = re.match(r'liver_\d+_(\d+)\.h5', filename)
    if match:
        return int(match.group(1))
    return None

def load_slice_data(file_path):
    """
    加载单个切片的数据
    """
    try:
        with h5py.File(file_path, 'r') as f:
            image = f['image'][:]
            label = f['label'][:]
            
            # 获取元数据
            metadata = {}
            for key, value in f.attrs.items():
                metadata[key] = value
                
            return {
                'image': image,
                'label': label,
                'metadata': metadata
            }
    except Exception as e:
        print(f"❌ 加载文件失败 {file_path}: {str(e)}")
        return None

def merge_single_patient(args):
    """
    合并单个病人的所有切片 - 多进程工作函数
    
    Args:
        args: tuple containing (patient_id, patient_files, output_dir)
    
    Returns:
        dict: 处理结果
    """
    patient_id, patient_files, output_dir = args
    
    try:
        print(f"🔄 [进程] 合并病人 liver_{patient_id} 的 {len(patient_files)} 个切片...")
        
        # 按切片编号排序
        patient_files.sort(key=lambda x: extract_slice_id(x))
        
        all_images = []
        all_labels = []
        slice_metadata = []
        
        # 加载所有切片
        for file_path in patient_files:
            slice_data = load_slice_data(file_path)
            if slice_data is None:
                print(f"⚠️  [进程] 跳过损坏的文件: {file_path}")
                continue
                
            all_images.append(slice_data['image'])
            all_labels.append(slice_data['label'])
            slice_metadata.append(slice_data['metadata'])
        
        if not all_images:
            print(f"❌ [进程] 病人 liver_{patient_id} 没有有效的切片数据")
            return {
                'patient_id': patient_id,
                'success': False,
                'error': 'No valid slice data',
                'num_slices': 0
            }
        
        # 转换为numpy数组 (slices, height, width)
        images_array = np.stack(all_images, axis=0)
        labels_array = np.stack(all_labels, axis=0)
        
        print(f"📊 [进程] liver_{patient_id} 合并后形状: images={images_array.shape}, labels={labels_array.shape}")
        
        # 生成输出路径
        output_filename = f"liver_{patient_id}.h5"
        output_path = os.path.join(output_dir, output_filename)
        
        # 保存合并后的文件
        with h5py.File(output_path, 'w') as f:
            # 保存图像和标签数据
            f.create_dataset('image', data=images_array, compression='gzip', compression_opts=9)
            f.create_dataset('label', data=labels_array, compression='gzip', compression_opts=9)
            
            # 保存病人级别的元数据
            f.attrs['patient_id'] = patient_id
            f.attrs['case_name'] = f'liver_{patient_id}'
            f.attrs['num_slices'] = len(all_images)
            f.attrs['image_shape'] = images_array.shape
            f.attrs['label_shape'] = labels_array.shape
            
            # 保存第一个切片的元数据作为参考
            if slice_metadata:
                first_metadata = slice_metadata[0]
                for key, value in first_metadata.items():
                    if key not in ['slice_index', 'slice_number']:  # 跳过切片特定的元数据
                        f.attrs[f'ref_{key}'] = value
            
            # 保存每个切片的原始索引信息
            slice_indices = []
            slice_numbers = []
            
            for metadata in slice_metadata:
                if 'slice_index' in metadata:
                    slice_indices.append(metadata['slice_index'])
                if 'slice_number' in metadata:
                    slice_numbers.append(metadata['slice_number'])
            
            if slice_indices:
                f.create_dataset('slice_indices', data=np.array(slice_indices))
            if slice_numbers:
                f.create_dataset('slice_numbers', data=np.array(slice_numbers))
        
        print(f"✅ [进程] 成功保存: {output_path}")
        
        return {
            'patient_id': patient_id,
            'success': True,
            'output_path': output_path,
            'num_slices': len(all_images),
            'original_files': len(patient_files),
            'valid_files': len(all_images)
        }
        
    except Exception as e:
        print(f"❌ [进程] 处理病人 liver_{patient_id} 时出错: {str(e)}")
        return {
            'patient_id': patient_id,
            'success': False,
            'error': str(e),
            'num_slices': 0
        }

def process_test_list_multi(test_list_file, output_dir, new_list_file, num_processes=8):
    """
    多进程处理测试列表文件
    """
    print(f"🔍 读取测试列表: {test_list_file}")
    
    if not os.path.exists(test_list_file):
        print(f"❌ 文件不存在: {test_list_file}")
        return
    
    # 创建输出目录
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 读取所有文件路径并按病人分组
    patient_files = defaultdict(list)
    
    with open(test_list_file, 'r') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
                
            patient_id = extract_patient_id(line)
            if patient_id is not None:
                patient_files[patient_id].append(line)
            else:
                print(f"⚠️  无法解析病人ID: {line}")
    
    print(f"📊 找到 {len(patient_files)} 个不同的病人")
    
    # 检查文件存在性并准备任务
    tasks = []
    for patient_id in sorted(patient_files.keys()):
        files = patient_files[patient_id]
        
        # 检查所有文件是否存在
        valid_files = []
        for file_path in files:
            if os.path.exists(file_path):
                valid_files.append(file_path)
            else:
                print(f"⚠️  文件不存在: {file_path}")
        
        if valid_files:
            tasks.append((patient_id, valid_files, str(output_dir)))
        else:
            print(f"❌ 病人 liver_{patient_id} 没有有效文件")
    
    print(f"📋 准备处理 {len(tasks)} 个有效病人")
    
    # 多进程处理
    print(f"🚀 启动 {num_processes} 个进程进行并行合并...")
    start_time = time.time()
    
    successful_patients = []
    failed_patients = []
    
    with Pool(processes=num_processes) as pool:
        # 使用imap_unordered获取进度
        with tqdm(total=len(tasks), desc="🔄 合并病例", unit="病例", 
                 bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]") as pbar:
            for result in pool.imap_unordered(merge_single_patient, tasks):
                if result['success']:
                    successful_patients.append(result['patient_id'])
                    pbar.set_postfix({
                        "当前": f"liver_{result['patient_id']}", 
                        "切片": result['num_slices'],
                        "成功": len(successful_patients),
                        "失败": len(failed_patients)
                    })
                else:
                    failed_patients.append(result)
                    pbar.set_postfix({
                        "失败": f"liver_{result['patient_id']}",
                        "成功": len(successful_patients),
                        "失败数": len(failed_patients)
                    })
                
                pbar.update(1)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # 生成新的列表文件
    print(f"\n📝 生成新的列表文件: {new_list_file}")
    
    with open(new_list_file, 'w') as f:
        for patient_id in sorted(successful_patients):
            output_filename = f"liver_{patient_id}.h5"
            output_path = output_dir / output_filename
            f.write(str(output_path) + '\n')
    
    # 输出统计信息
    print(f"\n🎉 处理完成!")
    print("=" * 60)
    print(f"📊 处理统计:")
    print(f"   总病人数: {len(tasks)}")
    print(f"   成功处理: {len(successful_patients)}")
    print(f"   失败处理: {len(failed_patients)}")
    print(f"   成功率: {len(successful_patients)/len(tasks)*100:.1f}%")
    print(f"   处理时间: {processing_time:.2f} 秒")
    print(f"   平均每个病人: {processing_time/len(tasks):.2f} 秒")
    print(f"   输出目录: {output_dir}")
    print(f"   新列表文件: {new_list_file}")
    
    # 显示失败的病人
    if failed_patients:
        print(f"\n❌ 处理失败的病人:")
        print("=" * 60)
        for i, failed in enumerate(failed_patients, 1):
            print(f"   {i:2d}. liver_{failed['patient_id']}: {failed.get('error', 'Unknown error')}")
    
    # 显示成功处理的病人
    print(f"\n📋 成功处理的病人:")
    for i, patient_id in enumerate(sorted(successful_patients)):
        files_count = len(patient_files[patient_id])
        print(f"   liver_{patient_id}: {files_count} 个切片")
        if i >= 9:  # 只显示前10个
            remaining = len(successful_patients) - 10
            if remaining > 0:
                print(f"   ... (还有 {remaining} 个病人)")
            break

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='2D测试数据切片合并 - 多进程版本')
    parser.add_argument('--test_list', default='/home/<USER>/data/liver_tumor/2d_all.list',
                       help='输入测试列表文件路径')
    parser.add_argument('--output_dir', default='/home/<USER>/data/liver_tumor/h5_2d_val',
                       help='输出目录路径')
    parser.add_argument('--new_list', default='/home/<USER>/data/liver_tumor/2d_train_merged.list',
                       help='新列表文件路径')
    parser.add_argument('--num_processes', type=int, default=8, help='并行处理进程数')

    args = parser.parse_args()

    print("🔄 2D测试数据切片合并 - 多进程版本")
    print("=" * 60)
    print(f"📁 输入列表: {args.test_list}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"📁 新列表文件: {args.new_list}")
    print(f"🔄 并行进程数: {args.num_processes}")
    print("=" * 60)

    # 处理测试列表
    process_test_list_multi(args.test_list, args.output_dir, args.new_list, args.num_processes)

if __name__ == "__main__":
    # 多进程保护
    import multiprocessing
    multiprocessing.set_start_method('spawn', force=True)
    main()
